/**
 * 图像处理节点
 * 提供图像加载、处理和编辑功能
 */

import { Node, SocketType, SocketDirection, NodeOptions } from '../nodes/Node';
import { AsyncNode } from '../nodes/AsyncNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 图像数据接口
 */
interface ImageData {
  width: number;
  height: number;
  data: Uint8Array | ArrayBuffer;
  format: string;
  url?: string;
}

/**
 * 加载图像节点
 */
export class LoadImageNode extends AsyncNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发加载'
    });

    this.addInput({
      name: 'source',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '图像源路径或URL',
      defaultValue: ''
    });

    this.addInput({
      name: 'format',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '图像格式',
      defaultValue: 'auto'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '加载成功'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '加载失败'
    });

    this.addOutput({
      name: 'image',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '加载的图像数据'
    });

    this.addOutput({
      name: 'errorMessage',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '错误信息'
    });
  }

  protected async executeImpl(): Promise<any> {
    const source = this.getInputValue('source');
    const format = this.getInputValue('format');

    try {
      const image = await this.loadImageAsync(source, format);
      
      this.setOutputValue('image', image);
      this.triggerOutput('success');
      
      return image;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      this.setOutputValue('errorMessage', errorMessage);
      this.triggerOutput('error');
      
      throw error;
    }
  }

  private async loadImageAsync(source: string, format: string): Promise<ImageData> {
    // 模拟图像加载
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (source) {
          resolve({
            width: 800,
            height: 600,
            data: new Uint8Array(800 * 600 * 4), // RGBA
            format: format === 'auto' ? 'png' : format,
            url: source
          });
        } else {
          reject(new Error('图像源路径不能为空'));
        }
      }, 200);
    });
  }
}

/**
 * 调整图像大小节点
 */
export class ResizeImageNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'image',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '输入图像'
    });

    this.addInput({
      name: 'width',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '目标宽度',
      defaultValue: 100
    });

    this.addInput({
      name: 'height',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '目标高度',
      defaultValue: 100
    });

    this.addInput({
      name: 'keepAspectRatio',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '保持宽高比',
      defaultValue: true
    });

    this.addOutput({
      name: 'resizedImage',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '调整大小后的图像'
    });
  }

  protected executeImpl(): any {
    const image = this.getInputValue('image') as ImageData;
    const targetWidth = this.getInputValue('width');
    const targetHeight = this.getInputValue('height');
    const keepAspectRatio = this.getInputValue('keepAspectRatio');

    if (!image) {
      throw new Error('输入图像不能为空');
    }

    let newWidth = targetWidth;
    let newHeight = targetHeight;

    if (keepAspectRatio) {
      const aspectRatio = image.width / image.height;
      if (targetWidth / targetHeight > aspectRatio) {
        newWidth = targetHeight * aspectRatio;
      } else {
        newHeight = targetWidth / aspectRatio;
      }
    }

    const resizedImage: ImageData = {
      width: newWidth,
      height: newHeight,
      data: new Uint8Array(newWidth * newHeight * 4),
      format: image.format,
      url: image.url
    };

    this.setOutputValue('resizedImage', resizedImage);
    return resizedImage;
  }
}

/**
 * 图像滤镜节点
 */
export class ImageFilterNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'image',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '输入图像'
    });

    this.addInput({
      name: 'filterType',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '滤镜类型',
      defaultValue: 'blur'
    });

    this.addInput({
      name: 'intensity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '滤镜强度',
      defaultValue: 1.0
    });

    this.addOutput({
      name: 'filteredImage',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '应用滤镜后的图像'
    });
  }

  protected executeImpl(): any {
    const image = this.getInputValue('image') as ImageData;
    const filterType = this.getInputValue('filterType');
    const intensity = this.getInputValue('intensity');

    if (!image) {
      throw new Error('输入图像不能为空');
    }

    // 模拟滤镜处理
    const filteredImage: ImageData = {
      ...image,
      data: new Uint8Array(image.data.length)
    };

    // 复制原始数据并应用滤镜效果
    if (image.data instanceof Uint8Array) {
      filteredImage.data.set(image.data);
    }

    this.setOutputValue('filteredImage', filteredImage);
    return filteredImage;
  }
}

/**
 * 裁剪图像节点
 */
export class CropImageNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'image',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '输入图像'
    });

    this.addInput({
      name: 'x',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '裁剪起始X坐标',
      defaultValue: 0
    });

    this.addInput({
      name: 'y',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '裁剪起始Y坐标',
      defaultValue: 0
    });

    this.addInput({
      name: 'width',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '裁剪宽度',
      defaultValue: 100
    });

    this.addInput({
      name: 'height',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '裁剪高度',
      defaultValue: 100
    });

    this.addOutput({
      name: 'croppedImage',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '裁剪后的图像'
    });
  }

  protected executeImpl(): any {
    const image = this.getInputValue('image') as ImageData;
    const x = this.getInputValue('x');
    const y = this.getInputValue('y');
    const width = this.getInputValue('width');
    const height = this.getInputValue('height');

    if (!image) {
      throw new Error('输入图像不能为空');
    }

    // 验证裁剪区域
    if (x < 0 || y < 0 || x + width > image.width || y + height > image.height) {
      throw new Error('裁剪区域超出图像边界');
    }

    const croppedImage: ImageData = {
      width,
      height,
      data: new Uint8Array(width * height * 4),
      format: image.format,
      url: image.url
    };

    this.setOutputValue('croppedImage', croppedImage);
    return croppedImage;
  }
}

/**
 * 旋转图像节点
 */
export class RotateImageNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'image',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '输入图像'
    });

    this.addInput({
      name: 'angle',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '旋转角度（度）',
      defaultValue: 90
    });

    this.addOutput({
      name: 'rotatedImage',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '旋转后的图像'
    });
  }

  protected executeImpl(): any {
    const image = this.getInputValue('image') as ImageData;
    const angle = this.getInputValue('angle');

    if (!image) {
      throw new Error('输入图像不能为空');
    }

    // 计算旋转后的尺寸
    const radians = (angle * Math.PI) / 180;
    const cos = Math.abs(Math.cos(radians));
    const sin = Math.abs(Math.sin(radians));
    const newWidth = Math.ceil(image.width * cos + image.height * sin);
    const newHeight = Math.ceil(image.width * sin + image.height * cos);

    const rotatedImage: ImageData = {
      width: newWidth,
      height: newHeight,
      data: new Uint8Array(newWidth * newHeight * 4),
      format: image.format,
      url: image.url
    };

    this.setOutputValue('rotatedImage', rotatedImage);
    return rotatedImage;
  }
}

/**
 * 注册图像处理节点
 */
export function registerImageProcessingNodes(registry: NodeRegistry): void {
  registry.registerNodeType({
    type: 'image/load',
    name: '加载图像',
    category: '图像处理',
    description: '从文件或URL加载图像',
    factory: (options: NodeOptions) => new LoadImageNode(options)
  });

  registry.registerNodeType({
    type: 'image/resize',
    name: '调整图像大小',
    category: '图像处理',
    description: '调整图像的宽度和高度',
    factory: (options: NodeOptions) => new ResizeImageNode(options)
  });

  registry.registerNodeType({
    type: 'image/filter',
    name: '图像滤镜',
    category: '图像处理',
    description: '对图像应用各种滤镜效果',
    factory: (options: NodeOptions) => new ImageFilterNode(options)
  });

  registry.registerNodeType({
    type: 'image/crop',
    name: '裁剪图像',
    category: '图像处理',
    description: '裁剪图像的指定区域',
    factory: (options: NodeOptions) => new CropImageNode(options)
  });

  registry.registerNodeType({
    type: 'image/rotate',
    name: '旋转图像',
    category: '图像处理',
    description: '按指定角度旋转图像',
    factory: (options: NodeOptions) => new RotateImageNode(options)
  });
}
