/**
 * UI节点
 * 提供用户界面组件创建和管理功能
 */

import { Node, SocketType, SocketDirection, NodeOptions } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 创建按钮节点
 */
export class CreateButtonNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发创建'
    });

    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '按钮文本',
      defaultValue: 'Button'
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '按钮位置',
      defaultValue: { x: 0, y: 0 }
    });

    this.addInput({
      name: 'size',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '按钮大小',
      defaultValue: { width: 100, height: 30 }
    });

    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建成功'
    });

    this.addOutput({
      name: 'button',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '创建的按钮对象'
    });
  }

  protected executeImpl(): any {
    const text = this.getInputValue('text');
    const position = this.getInputValue('position');
    const size = this.getInputValue('size');

    // 创建按钮对象
    const button = {
      type: 'button',
      text,
      position,
      size,
      id: `button_${Date.now()}`,
      onClick: () => this.emit('buttonClick', button)
    };

    this.setOutputValue('button', button);
    this.triggerOutput('success');

    return button;
  }
}

/**
 * 创建文本节点
 */
export class CreateTextNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发创建'
    });

    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '文本内容',
      defaultValue: 'Text'
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '文本位置',
      defaultValue: { x: 0, y: 0 }
    });

    this.addInput({
      name: 'style',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '文本样式',
      defaultValue: { fontSize: 16, color: '#000000' }
    });

    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建成功'
    });

    this.addOutput({
      name: 'textElement',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '创建的文本对象'
    });
  }

  protected executeImpl(): any {
    const text = this.getInputValue('text');
    const position = this.getInputValue('position');
    const style = this.getInputValue('style');

    const textElement = {
      type: 'text',
      text,
      position,
      style,
      id: `text_${Date.now()}`
    };

    this.setOutputValue('textElement', textElement);
    this.triggerOutput('success');

    return textElement;
  }
}

/**
 * 创建输入框节点
 */
export class CreateInputNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发创建'
    });

    this.addInput({
      name: 'placeholder',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '占位符文本',
      defaultValue: 'Enter text...'
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '输入框位置',
      defaultValue: { x: 0, y: 0 }
    });

    this.addInput({
      name: 'size',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '输入框大小',
      defaultValue: { width: 200, height: 30 }
    });

    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建成功'
    });

    this.addOutput({
      name: 'input',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '创建的输入框对象'
    });
  }

  protected executeImpl(): any {
    const placeholder = this.getInputValue('placeholder');
    const position = this.getInputValue('position');
    const size = this.getInputValue('size');

    const input = {
      type: 'input',
      placeholder,
      position,
      size,
      value: '',
      id: `input_${Date.now()}`,
      onChange: (value: string) => this.emit('inputChange', { input, value })
    };

    this.setOutputValue('input', input);
    this.triggerOutput('success');

    return input;
  }
}

/**
 * 创建滑块节点
 */
export class CreateSliderNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发创建'
    });

    this.addInput({
      name: 'min',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '最小值',
      defaultValue: 0
    });

    this.addInput({
      name: 'max',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '最大值',
      defaultValue: 100
    });

    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '初始值',
      defaultValue: 50
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '滑块位置',
      defaultValue: { x: 0, y: 0 }
    });

    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建成功'
    });

    this.addOutput({
      name: 'slider',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '创建的滑块对象'
    });
  }

  protected executeImpl(): any {
    const min = this.getInputValue('min');
    const max = this.getInputValue('max');
    const value = this.getInputValue('value');
    const position = this.getInputValue('position');

    const slider = {
      type: 'slider',
      min,
      max,
      value,
      position,
      id: `slider_${Date.now()}`,
      onChange: (newValue: number) => this.emit('sliderChange', { slider, value: newValue })
    };

    this.setOutputValue('slider', slider);
    this.triggerOutput('success');

    return slider;
  }
}

/**
 * 创建图像节点
 */
export class CreateImageNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发创建'
    });

    this.addInput({
      name: 'src',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '图像源路径',
      defaultValue: ''
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '图像位置',
      defaultValue: { x: 0, y: 0 }
    });

    this.addInput({
      name: 'size',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '图像大小',
      defaultValue: { width: 100, height: 100 }
    });

    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建成功'
    });

    this.addOutput({
      name: 'image',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '创建的图像对象'
    });
  }

  protected executeImpl(): any {
    const src = this.getInputValue('src');
    const position = this.getInputValue('position');
    const size = this.getInputValue('size');

    const image = {
      type: 'image',
      src,
      position,
      size,
      id: `image_${Date.now()}`,
      onLoad: () => this.emit('imageLoad', image),
      onError: (error: Error) => this.emit('imageError', { image, error })
    };

    this.setOutputValue('image', image);
    this.triggerOutput('success');

    return image;
  }
}

/**
 * 注册UI节点
 */
export function registerUINodes(registry: NodeRegistry): void {
  registry.registerNodeType({
    type: 'ui/button/create',
    name: '创建按钮',
    category: 'UI节点',
    description: '创建可点击的按钮组件',
    factory: (options: NodeOptions) => new CreateButtonNode(options)
  });

  registry.registerNodeType({
    type: 'ui/text/create',
    name: '创建文本',
    category: 'UI节点',
    description: '创建文本显示组件',
    factory: (options: NodeOptions) => new CreateTextNode(options)
  });

  registry.registerNodeType({
    type: 'ui/input/create',
    name: '创建输入框',
    category: 'UI节点',
    description: '创建文本输入框组件',
    factory: (options: NodeOptions) => new CreateInputNode(options)
  });

  registry.registerNodeType({
    type: 'ui/slider/create',
    name: '创建滑块',
    category: 'UI节点',
    description: '创建数值滑块组件',
    factory: (options: NodeOptions) => new CreateSliderNode(options)
  });

  registry.registerNodeType({
    type: 'ui/image/create',
    name: '创建图像',
    category: 'UI节点',
    description: '创建图像显示组件',
    factory: (options: NodeOptions) => new CreateImageNode(options)
  });
}
