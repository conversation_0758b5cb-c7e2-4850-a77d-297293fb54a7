/**
 * 数学节点
 * 提供完整的数学运算功能
 */

import { Node, SocketType, SocketDirection, NodeOptions } from '../nodes/Node';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 加法节点
 */
export class AddNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第一个数',
      defaultValue: 0
    });

    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第二个数',
      defaultValue: 0
    });

    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '计算结果'
    });
  }

  protected executeImpl(): any {
    const a = this.getInputValue('a');
    const b = this.getInputValue('b');
    const result = a + b;

    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 减法节点
 */
export class SubtractNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '被减数',
      defaultValue: 0
    });

    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '减数',
      defaultValue: 0
    });

    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '计算结果'
    });
  }

  protected executeImpl(): any {
    const a = this.getInputValue('a');
    const b = this.getInputValue('b');
    const result = a - b;

    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 乘法节点
 */
export class MultiplyNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第一个数',
      defaultValue: 1
    });

    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第二个数',
      defaultValue: 1
    });

    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '计算结果'
    });
  }

  protected executeImpl(): any {
    const a = this.getInputValue('a');
    const b = this.getInputValue('b');
    const result = a * b;

    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 除法节点
 */
export class DivideNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '被除数',
      defaultValue: 1
    });

    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '除数',
      defaultValue: 1
    });

    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '计算结果'
    });
  }

  protected executeImpl(): any {
    const a = this.getInputValue('a');
    const b = this.getInputValue('b');
    
    if (b === 0) {
      throw new Error('除数不能为零');
    }

    const result = a / b;
    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 幂运算节点
 */
export class PowerNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'base',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '底数',
      defaultValue: 2
    });

    this.addInput({
      name: 'exponent',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '指数',
      defaultValue: 2
    });

    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '计算结果'
    });
  }

  protected executeImpl(): any {
    const base = this.getInputValue('base');
    const exponent = this.getInputValue('exponent');
    const result = Math.pow(base, exponent);

    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 平方根节点
 */
export class SqrtNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '输入值',
      defaultValue: 4
    });

    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '平方根结果'
    });
  }

  protected executeImpl(): any {
    const value = this.getInputValue('value');
    
    if (value < 0) {
      throw new Error('不能计算负数的平方根');
    }

    const result = Math.sqrt(value);
    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 绝对值节点
 */
export class AbsNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '输入值',
      defaultValue: -5
    });

    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '绝对值结果'
    });
  }

  protected executeImpl(): any {
    const value = this.getInputValue('value');
    const result = Math.abs(value);

    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 三角函数节点
 */
export class TrigonometricNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'angle',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '角度（弧度）',
      defaultValue: 0
    });

    this.addInput({
      name: 'function',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '三角函数类型',
      defaultValue: 'sin'
    });

    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '计算结果'
    });
  }

  protected executeImpl(): any {
    const angle = this.getInputValue('angle');
    const func = this.getInputValue('function');

    let result: number;

    switch (func) {
      case 'sin':
        result = Math.sin(angle);
        break;
      case 'cos':
        result = Math.cos(angle);
        break;
      case 'tan':
        result = Math.tan(angle);
        break;
      case 'asin':
        result = Math.asin(angle);
        break;
      case 'acos':
        result = Math.acos(angle);
        break;
      case 'atan':
        result = Math.atan(angle);
        break;
      default:
        throw new Error(`不支持的三角函数: ${func}`);
    }

    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 随机数节点
 */
export class RandomNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'min',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '最小值',
      defaultValue: 0
    });

    this.addInput({
      name: 'max',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '最大值',
      defaultValue: 1
    });

    this.addInput({
      name: 'integer',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '是否为整数',
      defaultValue: false
    });

    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '随机数结果'
    });
  }

  protected executeImpl(): any {
    const min = this.getInputValue('min');
    const max = this.getInputValue('max');
    const integer = this.getInputValue('integer');

    let result = Math.random() * (max - min) + min;

    if (integer) {
      result = Math.floor(result);
    }

    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 向量运算节点
 */
export class VectorOperationNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'vectorA',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '向量A',
      defaultValue: { x: 0, y: 0, z: 0 }
    });

    this.addInput({
      name: 'vectorB',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '向量B',
      defaultValue: { x: 0, y: 0, z: 0 }
    });

    this.addInput({
      name: 'operation',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '运算类型',
      defaultValue: 'add'
    });

    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '运算结果'
    });
  }

  protected executeImpl(): any {
    const vectorA = this.getInputValue('vectorA');
    const vectorB = this.getInputValue('vectorB');
    const operation = this.getInputValue('operation');

    let result: any;

    switch (operation) {
      case 'add':
        result = {
          x: vectorA.x + vectorB.x,
          y: vectorA.y + vectorB.y,
          z: vectorA.z + vectorB.z
        };
        break;
      case 'subtract':
        result = {
          x: vectorA.x - vectorB.x,
          y: vectorA.y - vectorB.y,
          z: vectorA.z - vectorB.z
        };
        break;
      case 'dot':
        result = vectorA.x * vectorB.x + vectorA.y * vectorB.y + vectorA.z * vectorB.z;
        break;
      case 'cross':
        result = {
          x: vectorA.y * vectorB.z - vectorA.z * vectorB.y,
          y: vectorA.z * vectorB.x - vectorA.x * vectorB.z,
          z: vectorA.x * vectorB.y - vectorA.y * vectorB.x
        };
        break;
      case 'magnitude':
        result = Math.sqrt(vectorA.x * vectorA.x + vectorA.y * vectorA.y + vectorA.z * vectorA.z);
        break;
      default:
        throw new Error(`不支持的向量运算: ${operation}`);
    }

    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 注册数学节点
 */
export function registerMathNodes(registry: NodeRegistry): void {
  registry.registerNodeType({
    type: 'math/basic/add',
    name: '加法',
    category: '数学运算',
    description: '计算两个数的和',
    factory: (options: NodeOptions) => new AddNode(options)
  });

  registry.registerNodeType({
    type: 'math/basic/subtract',
    name: '减法',
    category: '数学运算',
    description: '计算两个数的差',
    factory: (options: NodeOptions) => new SubtractNode(options)
  });

  registry.registerNodeType({
    type: 'math/basic/multiply',
    name: '乘法',
    category: '数学运算',
    description: '计算两个数的积',
    factory: (options: NodeOptions) => new MultiplyNode(options)
  });

  registry.registerNodeType({
    type: 'math/basic/divide',
    name: '除法',
    category: '数学运算',
    description: '计算两个数的商',
    factory: (options: NodeOptions) => new DivideNode(options)
  });

  registry.registerNodeType({
    type: 'math/advanced/power',
    name: '幂运算',
    category: '数学运算',
    description: '计算底数的指数次幂',
    factory: (options: NodeOptions) => new PowerNode(options)
  });

  registry.registerNodeType({
    type: 'math/advanced/sqrt',
    name: '平方根',
    category: '数学运算',
    description: '计算数值的平方根',
    factory: (options: NodeOptions) => new SqrtNode(options)
  });

  registry.registerNodeType({
    type: 'math/advanced/abs',
    name: '绝对值',
    category: '数学运算',
    description: '计算数值的绝对值',
    factory: (options: NodeOptions) => new AbsNode(options)
  });

  registry.registerNodeType({
    type: 'math/trigonometric',
    name: '三角函数',
    category: '数学运算',
    description: '计算三角函数值',
    factory: (options: NodeOptions) => new TrigonometricNode(options)
  });

  registry.registerNodeType({
    type: 'math/random',
    name: '随机数',
    category: '数学运算',
    description: '生成指定范围的随机数',
    factory: (options: NodeOptions) => new RandomNode(options)
  });

  registry.registerNodeType({
    type: 'math/vector',
    name: '向量运算',
    category: '数学运算',
    description: '执行向量数学运算',
    factory: (options: NodeOptions) => new VectorOperationNode(options)
  });
}
