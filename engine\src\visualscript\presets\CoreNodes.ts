/**
 * 核心节点
 * 提供视觉脚本的基础流程控制和数据操作功能
 */

import { Node, SocketType, SocketDirection } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { EventNode } from '../nodes/EventNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeOptions } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 开始事件节点
 * 脚本执行的入口点
 */
export class OnStartNode extends EventNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addOutput({
      name: 'start',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '脚本开始执行时触发'
    });
  }

  protected executeImpl(): any {
    // 开始节点在脚本启动时自动执行
    this.triggerOutput('start');
    return true;
  }
}

/**
 * 更新事件节点
 * 每帧执行一次
 */
export class OnUpdateNode extends EventNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'deltaTime',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '帧间隔时间（秒）',
      defaultValue: 0.016
    });

    this.addOutput({
      name: 'update',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '每帧更新时触发'
    });

    this.addOutput({
      name: 'deltaTime',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '帧间隔时间'
    });
  }

  protected executeImpl(): any {
    const deltaTime = this.getInputValue('deltaTime');
    this.setOutputValue('deltaTime', deltaTime);
    this.triggerOutput('update');
    return deltaTime;
  }
}

/**
 * 序列节点
 * 按顺序依次执行多个操作
 */
export class SequenceNode extends FlowNode {
  private currentIndex: number = 0;

  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });

    // 动态输出端口（可配置数量）
    for (let i = 0; i < 5; i++) {
      this.addOutput({
        name: `output${i}`,
        type: SocketType.FLOW,
        direction: SocketDirection.OUTPUT,
        description: `第${i + 1}个输出`,
        optional: true
      });
    }
  }

  protected executeImpl(): any {
    // 按顺序触发所有连接的输出
    const outputs = this.getConnectedOutputs();
    
    for (const outputName of outputs) {
      this.triggerOutput(outputName);
    }

    return true;
  }

  private getConnectedOutputs(): string[] {
    const connected: string[] = [];
    for (let i = 0; i < 5; i++) {
      const outputName = `output${i}`;
      if (this.hasConnectedOutput(outputName)) {
        connected.push(outputName);
      }
    }
    return connected;
  }
}

/**
 * 分支节点
 * 根据条件选择执行路径
 */
export class BranchNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });

    this.addInput({
      name: 'condition',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '分支条件',
      defaultValue: false
    });

    this.addOutput({
      name: 'true',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '条件为真时执行'
    });

    this.addOutput({
      name: 'false',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '条件为假时执行'
    });
  }

  protected executeImpl(): any {
    const condition = this.getInputValue('condition');
    
    if (condition) {
      this.triggerOutput('true');
    } else {
      this.triggerOutput('false');
    }

    return condition;
  }
}

/**
 * 延迟节点
 * 延迟指定时间后执行
 */
export class DelayNode extends AsyncNode {
  private timeoutId: NodeJS.Timeout | null = null;

  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });

    this.addInput({
      name: 'delay',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '延迟时间（秒）',
      defaultValue: 1.0
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '延迟完成后执行'
    });
  }

  protected async executeImpl(): Promise<any> {
    const delay = this.getInputValue('delay') * 1000; // 转换为毫秒

    return new Promise((resolve) => {
      this.timeoutId = setTimeout(() => {
        this.triggerOutput('completed');
        resolve(true);
      }, delay);
    });
  }

  public dispose(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
    super.dispose();
  }
}

/**
 * For循环节点
 * 执行指定次数的循环
 */
export class ForLoopNode extends FlowNode {
  private currentIndex: number = 0;

  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });

    this.addInput({
      name: 'count',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '循环次数',
      defaultValue: 10
    });

    this.addOutput({
      name: 'loopBody',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '循环体执行'
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '循环完成'
    });

    this.addOutput({
      name: 'index',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '当前循环索引'
    });
  }

  protected executeImpl(): any {
    const count = this.getInputValue('count');
    
    for (this.currentIndex = 0; this.currentIndex < count; this.currentIndex++) {
      this.setOutputValue('index', this.currentIndex);
      this.triggerOutput('loopBody');
    }

    this.triggerOutput('completed');
    return count;
  }
}

/**
 * While循环节点
 * 根据条件执行循环
 */
export class WhileLoopNode extends FlowNode {
  private maxIterations: number = 1000; // 防止无限循环

  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });

    this.addInput({
      name: 'condition',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '循环条件',
      defaultValue: false
    });

    this.addOutput({
      name: 'loopBody',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '循环体执行'
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '循环完成'
    });
  }

  protected executeImpl(): any {
    let iterations = 0;
    
    while (this.getInputValue('condition') && iterations < this.maxIterations) {
      this.triggerOutput('loopBody');
      iterations++;
    }

    this.triggerOutput('completed');
    return iterations;
  }
}

/**
 * 注册核心节点
 */
export function registerCoreNodes(registry: NodeRegistry): void {
  registry.registerNodeType({
    type: 'core/events/onStart',
    name: '开始事件',
    category: '核心节点',
    description: '脚本开始执行时触发',
    factory: (options: NodeOptions) => new OnStartNode(options)
  });

  registry.registerNodeType({
    type: 'core/events/onUpdate',
    name: '更新事件',
    category: '核心节点',
    description: '每帧更新时触发',
    factory: (options: NodeOptions) => new OnUpdateNode(options)
  });

  registry.registerNodeType({
    type: 'core/flow/sequence',
    name: '序列',
    category: '核心节点',
    description: '按顺序执行多个操作',
    factory: (options: NodeOptions) => new SequenceNode(options)
  });

  registry.registerNodeType({
    type: 'core/flow/branch',
    name: '分支',
    category: '核心节点',
    description: '根据条件选择执行路径',
    factory: (options: NodeOptions) => new BranchNode(options)
  });

  registry.registerNodeType({
    type: 'core/flow/delay',
    name: '延迟',
    category: '核心节点',
    description: '延迟指定时间后执行',
    factory: (options: NodeOptions) => new DelayNode(options)
  });

  registry.registerNodeType({
    type: 'core/flow/forLoop',
    name: 'For循环',
    category: '核心节点',
    description: '执行指定次数的循环',
    factory: (options: NodeOptions) => new ForLoopNode(options)
  });

  registry.registerNodeType({
    type: 'core/flow/whileLoop',
    name: 'While循环',
    category: '核心节点',
    description: '根据条件执行循环',
    factory: (options: NodeOptions) => new WhileLoopNode(options)
  });
}
