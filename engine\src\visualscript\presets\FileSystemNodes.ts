/**
 * 文件系统节点
 * 提供文件和目录操作功能
 */

import { Node, SocketType, SocketDirection, NodeOptions } from '../nodes/Node';
import { AsyncNode } from '../nodes/AsyncNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 读取文本文件节点
 */
export class ReadTextFileNode extends AsyncNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发读取'
    });

    this.addInput({
      name: 'filePath',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '文件路径',
      defaultValue: ''
    });

    this.addInput({
      name: 'encoding',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '文件编码',
      defaultValue: 'utf-8'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '读取成功'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '读取失败'
    });

    this.addOutput({
      name: 'content',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '文件内容'
    });

    this.addOutput({
      name: 'errorMessage',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '错误信息'
    });
  }

  protected async executeImpl(): Promise<any> {
    const filePath = this.getInputValue('filePath');
    const encoding = this.getInputValue('encoding');

    try {
      // 模拟文件读取（在实际实现中会使用真实的文件系统API）
      const content = await this.readFileAsync(filePath, encoding);
      
      this.setOutputValue('content', content);
      this.triggerOutput('success');
      
      return content;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      this.setOutputValue('errorMessage', errorMessage);
      this.triggerOutput('error');
      
      throw error;
    }
  }

  private async readFileAsync(filePath: string, encoding: string): Promise<string> {
    // 这里应该实现真实的文件读取逻辑
    // 目前返回模拟数据
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (filePath) {
          resolve(`File content from ${filePath} with encoding ${encoding}`);
        } else {
          reject(new Error('文件路径不能为空'));
        }
      }, 100);
    });
  }
}

/**
 * 写入文本文件节点
 */
export class WriteTextFileNode extends AsyncNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发写入'
    });

    this.addInput({
      name: 'filePath',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '文件路径',
      defaultValue: ''
    });

    this.addInput({
      name: 'content',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '文件内容',
      defaultValue: ''
    });

    this.addInput({
      name: 'encoding',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '文件编码',
      defaultValue: 'utf-8'
    });

    this.addInput({
      name: 'append',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '是否追加模式',
      defaultValue: false
    });

    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '写入成功'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '写入失败'
    });

    this.addOutput({
      name: 'bytesWritten',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '写入字节数'
    });

    this.addOutput({
      name: 'errorMessage',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '错误信息'
    });
  }

  protected async executeImpl(): Promise<any> {
    const filePath = this.getInputValue('filePath');
    const content = this.getInputValue('content');
    const encoding = this.getInputValue('encoding');
    const append = this.getInputValue('append');

    try {
      const bytesWritten = await this.writeFileAsync(filePath, content, encoding, append);
      
      this.setOutputValue('bytesWritten', bytesWritten);
      this.triggerOutput('success');
      
      return bytesWritten;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      this.setOutputValue('errorMessage', errorMessage);
      this.triggerOutput('error');
      
      throw error;
    }
  }

  private async writeFileAsync(filePath: string, content: string, encoding: string, append: boolean): Promise<number> {
    // 这里应该实现真实的文件写入逻辑
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (filePath && content !== undefined) {
          const bytesWritten = content.length;
          resolve(bytesWritten);
        } else {
          reject(new Error('文件路径和内容不能为空'));
        }
      }, 100);
    });
  }
}

/**
 * 检查文件存在节点
 */
export class FileExistsNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'filePath',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '文件路径',
      defaultValue: ''
    });

    this.addOutput({
      name: 'exists',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '文件是否存在'
    });

    this.addOutput({
      name: 'isFile',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否为文件'
    });

    this.addOutput({
      name: 'isDirectory',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否为目录'
    });
  }

  protected executeImpl(): any {
    const filePath = this.getInputValue('filePath');
    
    // 模拟文件存在检查
    const exists = Boolean(filePath);
    const isFile = exists && !filePath.endsWith('/');
    const isDirectory = exists && filePath.endsWith('/');

    this.setOutputValue('exists', exists);
    this.setOutputValue('isFile', isFile);
    this.setOutputValue('isDirectory', isDirectory);

    return { exists, isFile, isDirectory };
  }
}

/**
 * 创建目录节点
 */
export class CreateDirectoryNode extends AsyncNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发创建'
    });

    this.addInput({
      name: 'dirPath',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '目录路径',
      defaultValue: ''
    });

    this.addInput({
      name: 'recursive',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '是否递归创建',
      defaultValue: true
    });

    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建成功'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建失败'
    });

    this.addOutput({
      name: 'created',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否新创建'
    });

    this.addOutput({
      name: 'errorMessage',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '错误信息'
    });
  }

  protected async executeImpl(): Promise<any> {
    const dirPath = this.getInputValue('dirPath');
    const recursive = this.getInputValue('recursive');

    try {
      const created = await this.createDirectoryAsync(dirPath, recursive);
      
      this.setOutputValue('created', created);
      this.triggerOutput('success');
      
      return created;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      this.setOutputValue('errorMessage', errorMessage);
      this.triggerOutput('error');
      
      throw error;
    }
  }

  private async createDirectoryAsync(dirPath: string, recursive: boolean): Promise<boolean> {
    // 这里应该实现真实的目录创建逻辑
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (dirPath) {
          resolve(true); // 模拟创建成功
        } else {
          reject(new Error('目录路径不能为空'));
        }
      }, 50);
    });
  }
}

/**
 * 删除文件节点
 */
export class DeleteFileNode extends AsyncNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发删除'
    });

    this.addInput({
      name: 'filePath',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '文件路径',
      defaultValue: ''
    });

    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '删除成功'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '删除失败'
    });

    this.addOutput({
      name: 'deleted',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否已删除'
    });

    this.addOutput({
      name: 'errorMessage',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '错误信息'
    });
  }

  protected async executeImpl(): Promise<any> {
    const filePath = this.getInputValue('filePath');

    try {
      const deleted = await this.deleteFileAsync(filePath);
      
      this.setOutputValue('deleted', deleted);
      this.triggerOutput('success');
      
      return deleted;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      this.setOutputValue('errorMessage', errorMessage);
      this.triggerOutput('error');
      
      throw error;
    }
  }

  private async deleteFileAsync(filePath: string): Promise<boolean> {
    // 这里应该实现真实的文件删除逻辑
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (filePath) {
          resolve(true); // 模拟删除成功
        } else {
          reject(new Error('文件路径不能为空'));
        }
      }, 50);
    });
  }
}

/**
 * 注册文件系统节点
 */
export function registerFileSystemNodes(registry: NodeRegistry): void {
  registry.registerNodeType({
    type: 'filesystem/file/readText',
    name: '读取文本文件',
    category: '文件系统',
    description: '从文件系统读取文本文件内容',
    factory: (options: NodeOptions) => new ReadTextFileNode(options)
  });

  registry.registerNodeType({
    type: 'filesystem/file/writeText',
    name: '写入文本文件',
    category: '文件系统',
    description: '将文本内容写入文件',
    factory: (options: NodeOptions) => new WriteTextFileNode(options)
  });

  registry.registerNodeType({
    type: 'filesystem/file/exists',
    name: '检查文件存在',
    category: '文件系统',
    description: '检查文件或目录是否存在',
    factory: (options: NodeOptions) => new FileExistsNode(options)
  });

  registry.registerNodeType({
    type: 'filesystem/directory/create',
    name: '创建目录',
    category: '文件系统',
    description: '创建新目录',
    factory: (options: NodeOptions) => new CreateDirectoryNode(options)
  });

  registry.registerNodeType({
    type: 'filesystem/file/delete',
    name: '删除文件',
    category: '文件系统',
    description: '删除指定文件',
    factory: (options: NodeOptions) => new DeleteFileNode(options)
  });
}
