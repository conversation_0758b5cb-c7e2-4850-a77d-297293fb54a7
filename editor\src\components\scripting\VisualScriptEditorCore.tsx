/**
 * 可视化脚本编辑器核心渲染组件
 * 包含画布渲染、工具栏和面板等UI组件
 */
import React, { useCallback } from 'react';
import {
  Button,
  Space,
  Tooltip,
  Typography,
  Card,
  Divider,
  Badge,
  Progress,
  Tag
} from 'antd';
import {
  PlayCircleOutlined,
  StopOutlined,
  SaveOutlined,
  LoadingOutlined,
  PlusOutlined,
  SettingOutlined,
  FullscreenOutlined,
  UndoOutlined,
  RedoOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  ExpandOutlined,
  BugOutlined,
  BorderOutlined,
  NodeIndexOutlined,
  LinkOutlined,
  DeleteOutlined,
  CopyOutlined,
  EyeOutlined,
  CodeOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;

/**
 * 工具栏组件属性
 */
interface ToolbarProps {
  isExecuting: boolean;
  readOnly: boolean;
  zoomLevel: number;
  gridEnabled: boolean;
  showDebugPanel: boolean;
  hasUnsavedChanges: boolean;
  onExecute: () => void;
  onStop: () => void;
  onAddNode: () => void;
  onSave: () => void;
  onUndo: () => void;
  onRedo: () => void;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onFitToScreen: () => void;
  onToggleDebugPanel: () => void;
  onToggleGrid: () => void;
  onToggleSettings: () => void;
  onToggleFullscreen: () => void;
}

/**
 * 工具栏组件
 */
export const VisualScriptToolbar: React.FC<ToolbarProps> = ({
  isExecuting,
  readOnly,
  zoomLevel,
  gridEnabled,
  showDebugPanel,
  hasUnsavedChanges,
  onExecute,
  onStop,
  onAddNode,
  onSave,
  onUndo,
  onRedo,
  onZoomIn,
  onZoomOut,
  onFitToScreen,
  onToggleDebugPanel,
  onToggleGrid,
  onToggleSettings,
  onToggleFullscreen
}) => {
  const { t } = useTranslation();

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'space-between', 
      alignItems: 'center',
      padding: '8px 16px',
      borderBottom: '1px solid #f0f0f0',
      backgroundColor: '#fafafa'
    }}>
      <Space>
        <Tooltip title={t('可视化脚本.执行脚本')}>
          <Button
            type="primary"
            icon={isExecuting ? <LoadingOutlined /> : <PlayCircleOutlined />}
            onClick={onExecute}
            disabled={isExecuting || readOnly}
            loading={isExecuting}
          >
            {t('可视化脚本.执行')}
          </Button>
        </Tooltip>

        <Tooltip title={t('可视化脚本.停止脚本')}>
          <Button
            icon={<StopOutlined />}
            onClick={onStop}
            disabled={!isExecuting}
          >
            {t('可视化脚本.停止')}
          </Button>
        </Tooltip>

        <Divider type="vertical" />

        <Tooltip title={t('可视化脚本.添加节点')}>
          <Button
            icon={<PlusOutlined />}
            onClick={onAddNode}
            disabled={readOnly}
          >
            {t('可视化脚本.添加节点')}
          </Button>
        </Tooltip>

        <Tooltip title={t('可视化脚本.撤销')}>
          <Button
            icon={<UndoOutlined />}
            onClick={onUndo}
            disabled={readOnly}
          />
        </Tooltip>

        <Tooltip title={t('可视化脚本.重做')}>
          <Button
            icon={<RedoOutlined />}
            onClick={onRedo}
            disabled={readOnly}
          />
        </Tooltip>
      </Space>
      
      <Space>
        <Tooltip title={t('可视化脚本.放大')}>
          <Button
            icon={<ZoomInOutlined />}
            onClick={onZoomIn}
          />
        </Tooltip>

        <span style={{ fontSize: '12px', minWidth: '50px', textAlign: 'center' }}>
          {Math.round(zoomLevel * 100)}%
        </span>

        <Tooltip title={t('可视化脚本.缩小')}>
          <Button
            icon={<ZoomOutOutlined />}
            onClick={onZoomOut}
          />
        </Tooltip>

        <Tooltip title={t('可视化脚本.适应屏幕')}>
          <Button
            icon={<ExpandOutlined />}
            onClick={onFitToScreen}
          />
        </Tooltip>

        <Divider type="vertical" />

        <Tooltip title={t('可视化脚本.调试面板')}>
          <Button
            icon={<BugOutlined />}
            onClick={onToggleDebugPanel}
            type={showDebugPanel ? 'primary' : 'default'}
          />
        </Tooltip>

        <Tooltip title={t('可视化脚本.网格')}>
          <Button
            icon={<BorderOutlined />}
            onClick={onToggleGrid}
            type={gridEnabled ? 'primary' : 'default'}
          />
        </Tooltip>

        <Tooltip title={t('可视化脚本.保存脚本')}>
          <Button
            icon={<SaveOutlined />}
            onClick={onSave}
            disabled={readOnly}
            type={hasUnsavedChanges ? 'primary' : 'default'}
          >
            {t('可视化脚本.保存')}
          </Button>
        </Tooltip>

        <Tooltip title={t('可视化脚本.设置')}>
          <Button
            type="text"
            icon={<SettingOutlined />}
            onClick={onToggleSettings}
          />
        </Tooltip>

        <Tooltip title={t('可视化脚本.全屏')}>
          <Button
            type="text"
            icon={<FullscreenOutlined />}
            onClick={onToggleFullscreen}
          />
        </Tooltip>
      </Space>
    </div>
  );
};

/**
 * 性能监控面板属性
 */
interface PerformancePanelProps {
  metrics: {
    nodeCount: number;
    connectionCount: number;
    executionTime: number;
    memoryUsage: number;
  };
  isExecuting: boolean;
}

/**
 * 性能监控面板
 */
export const PerformancePanel: React.FC<PerformancePanelProps> = ({
  metrics,
  isExecuting
}) => {
  const { t } = useTranslation();

  return (
    <Card 
      title={
        <Space>
          <ThunderboltOutlined />
          {t('可视化脚本.性能监控')}
          {isExecuting && <Badge status="processing" />}
        </Space>
      }
      size="small"
      style={{ marginBottom: 16 }}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <Text type="secondary">{t('可视化脚本.节点数量')}: </Text>
          <Tag color="blue">{metrics.nodeCount}</Tag>
        </div>
        
        <div>
          <Text type="secondary">{t('可视化脚本.连接数量')}: </Text>
          <Tag color="green">{metrics.connectionCount}</Tag>
        </div>
        
        <div>
          <Text type="secondary">{t('可视化脚本.执行时间')}: </Text>
          <Tag color="orange">{metrics.executionTime.toFixed(2)}ms</Tag>
        </div>
        
        <div>
          <Text type="secondary">{t('可视化脚本.内存使用')}: </Text>
          <Progress 
            percent={Math.min(metrics.memoryUsage, 100)} 
            size="small" 
            status={metrics.memoryUsage > 80 ? 'exception' : 'normal'}
          />
        </div>
      </Space>
    </Card>
  );
};

/**
 * 调试日志面板属性
 */
interface DebugLogsPanelProps {
  logs: Array<{
    timestamp: number;
    level: 'info' | 'warn' | 'error';
    message: string;
    nodeId?: string;
  }>;
  onClear: () => void;
}

/**
 * 调试日志面板
 */
export const DebugLogsPanel: React.FC<DebugLogsPanelProps> = ({
  logs,
  onClear
}) => {
  const { t } = useTranslation();

  const getLogColor = (level: string) => {
    switch (level) {
      case 'error': return '#ff4d4f';
      case 'warn': return '#faad14';
      case 'info': return '#1890ff';
      default: return '#666';
    }
  };

  return (
    <Card 
      title={
        <Space>
          <CodeOutlined />
          {t('可视化脚本.调试日志')}
          <Badge count={logs.length} />
        </Space>
      }
      size="small"
      extra={
        <Button size="small" onClick={onClear}>
          {t('可视化脚本.清空日志')}
        </Button>
      }
    >
      <div style={{ 
        maxHeight: 200, 
        overflowY: 'auto',
        fontSize: '12px',
        fontFamily: 'monospace'
      }}>
        {logs.length === 0 ? (
          <Text type="secondary">{t('可视化脚本.暂无日志')}</Text>
        ) : (
          logs.slice(-50).map((log, index) => (
            <div key={index} style={{ 
              marginBottom: 4,
              color: getLogColor(log.level),
              borderLeft: `3px solid ${getLogColor(log.level)}`,
              paddingLeft: 8
            }}>
              <Text style={{ color: '#999', fontSize: '10px' }}>
                {new Date(log.timestamp).toLocaleTimeString()}
              </Text>
              {' '}
              <Text style={{ color: getLogColor(log.level) }}>
                [{log.level.toUpperCase()}]
              </Text>
              {' '}
              {log.message}
              {log.nodeId && (
                <Text style={{ color: '#999' }}> (节点: {log.nodeId})</Text>
              )}
            </div>
          ))
        )}
      </div>
    </Card>
  );
};
